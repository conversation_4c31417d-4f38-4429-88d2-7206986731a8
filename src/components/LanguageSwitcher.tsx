'use client';

import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useTransition } from 'react';

export default function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale(); // Get current locale
  const [isPending, startTransition] = useTransition();

  const switchLanguage = (nextLocale: string) => {
    startTransition(() => {
      // Remove current locale from pathname and add new locale
      const segments = pathname.split('/').filter(Boolean);
      if (segments[0] === 'zh' || segments[0] === 'ja') {
        segments[0] = nextLocale;
      } else {
        segments.unshift(nextLocale);
      }
      const newPath = '/' + segments.join('/');
      router.replace(newPath);
    });
  };

  return (
    <div className="flex space-x-2 items-center">
      <button
        onClick={() => switchLanguage('zh')}
        disabled={isPending || locale === 'zh'}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors
                    ${locale === 'zh' ? 'bg-blue-600 text-white cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
                    ${isPending ? 'opacity-50 cursor-default' : ''}`}
      >
        中文
      </button>
      <button
        onClick={() => switchLanguage('ja')}
        disabled={isPending || locale === 'ja'}
        className={`px-3 py-1 rounded-md text-sm font-medium transition-colors
                    ${locale === 'ja' ? 'bg-blue-600 text-white cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
                    ${isPending ? 'opacity-50 cursor-default' : ''}`}
      >
        日本語
      </button>
    </div>
  );
}
