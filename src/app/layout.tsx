import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server'; // Or from your i18n setup
// import { useLocale } from "next-intl"; // To get current locale for html lang
// useLocale hook is for client components, locale is passed via params in app router

// It's generally better to define metadata generation as a function
// if you need to make it dynamic based on locale, but for now,
// we'll keep the static metadata and assume titles/descriptions
// will be handled within page components or a dynamic metadata setup later.

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "赴日人才交流", // This will be overridden by page-specific translations
  description: "专业的赴日人才交流服务平台，提供赴日介护、护士等工作机会", // Same as above
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default async function RootLayout({
  children,
  params: {locale} // Next.js passes locale via params when using app router with next-intl
}: Readonly<{
  children: React.ReactNode;
  params: {locale: string};
}>) {
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextIntlClientProvider locale={locale} messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
