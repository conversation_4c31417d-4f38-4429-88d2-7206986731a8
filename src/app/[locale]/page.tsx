'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import LanguageSwitcher from '@/components/LanguageSwitcher'; // Adjust path if necessary

function HomeContent() {
  const [activeTab, setActiveTab] = useState('home');
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('Page');
  const locale = useLocale();

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['home', 'care', 'nurse', 'about'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabId);
    window.history.replaceState({}, '', url.toString());
  };

  const tabs = [
    { id: 'home', label: t('homeTab') },
    { id: 'care', label: t('careTab') },
    { id: 'nurse', label: t('nurseTab') },
    { id: 'about', label: t('aboutTab') }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('welcomeMessage')}</h2>
              <p className="text-gray-600 leading-relaxed">
                {t('welcomeDescription')}
              </p>
            </div>

            <div className="grid gap-4">
              <div
                className="bg-blue-50 p-4 rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                onClick={() => handleTabChange('care')}
              >
                <h3 className="font-semibold text-blue-800 mb-2">{t('careSectionTitle')}</h3>
                <p className="text-blue-700 text-sm">{t('careSectionDescription')}</p>
              </div>

              <div
                className="bg-green-50 p-4 rounded-lg border border-green-200 cursor-pointer hover:bg-green-100 transition-colors"
                onClick={() => handleTabChange('nurse')}
              >
                <h3 className="font-semibold text-green-800 mb-2">{t('nurseSectionTitle')}</h3>
                <p className="text-green-700 text-sm">{t('nurseSectionDescription')}</p>
              </div>
            </div>

            {/* 通知功能 */}
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-600 text-lg">📢</span>
                <div>
                  <h3 className="font-semibold text-yellow-800 mb-1">{t('latestNotification')}</h3>
                  <p className="text-yellow-700 text-sm">{t('notificationContent')}</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'care':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('careTitle')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careIndustryProspectsTitle')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {t('careIndustryProspectsDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careWorkContentTitle')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>{t('careWorkContentItem1')}</li>
                  <li>{t('careWorkContentItem2')}</li>
                  <li>{t('careWorkContentItem3')}</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careApplicationConditionsTitle')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>{t('careApplicationConditionsItem1')}</li>
                  <li>{t('careApplicationConditionsItem2')}</li>
                  <li>{t('careApplicationConditionsItem3')}</li>
                  <li>{t('careApplicationConditionsItem4')}</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careSalaryTitle')}</h3>
                <p className="text-gray-600 text-sm">{t('careSalaryDescription')}</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careVisaTypeTitle')}</h3>
                <p className="text-gray-600 text-sm">{t('careVisaTypeDescription')}</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careFeeStandardTitle')}</h3>
                <p className="text-gray-600 text-sm">
                  {t('careFeeStandardDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careFutureDirectionTitle')}</h3>
                <p className="text-gray-600 text-sm">
                  {t('careFutureDirectionDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('careApplicationProcessTitle')}</h3>
                <div className="text-gray-600 text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">1</span>
                    <span>{t('careApplicationProcessStep1')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">2</span>
                    <span>{t('careApplicationProcessStep2')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">3</span>
                    <span>{t('careApplicationProcessStep3')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">4</span>
                    <span>{t('careApplicationProcessStep4')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">5</span>
                    <span>{t('careApplicationProcessStep5')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">6</span>
                    <span>{t('careApplicationProcessStep6')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">7</span>
                    <span>{t('careApplicationProcessStep7')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">8</span>
                    <span>{t('careApplicationProcessStep8')}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert(t('faqButton') + '功能开发中，敬请期待！')}
                >
                  {t('faqButton')}
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push(`/${locale}/consultation`)}
                >
                  {t('consultButton')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'nurse':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('nurseTitle')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseIndustryProspectsTitle')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {t('nurseIndustryProspectsDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseWorkContentTitle')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>{t('nurseWorkContentItem1')}</li>
                  <li>{t('nurseWorkContentItem2')}</li>
                  <li>{t('nurseWorkContentItem3')}</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseApplicationConditionsTitle')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>{t('nurseApplicationConditionsItem1')}</li>
                  <li>{t('nurseApplicationConditionsItem2')}</li>
                  <li>{t('nurseApplicationConditionsItem3')}</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseSalaryTitle')}</h3>
                <p className="text-gray-600 text-sm">{t('nurseSalaryDescription')}</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseVisaTypeTitle')}</h3>
                <p className="text-gray-600 text-sm">{t('nurseVisaTypeDescription')}</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseFeeStandardTitle')}</h3>
                <p className="text-gray-600 text-sm">
                  {t('nurseFeeStandardDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseFutureDirectionTitle')}</h3>
                <p className="text-gray-600 text-sm">
                  {t('nurseFutureDirectionDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurseApplicationProcessTitle')}</h3>
                <div className="text-gray-600 text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">1</span>
                    <span>{t('careApplicationProcessStep1')}</span> {/* Assuming same steps for nurse */}
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">2</span>
                    <span>{t('careApplicationProcessStep2')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">3</span>
                    <span>{t('careApplicationProcessStep3')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">4</span>
                    <span>{t('careApplicationProcessStep4')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">5</span>
                    <span>{t('careApplicationProcessStep5')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">6</span>
                    <span>{t('careApplicationProcessStep6')}</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">7</span>
                    <span>{t('careApplicationProcessStep7')}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">8</span>
                    <span>{t('careApplicationProcessStep8')}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert(t('faqButton') + '功能开发中，敬请期待！')}
                >
                  {t('faqButton')}
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push(`/${locale}/consultation`)}
                >
                  {t('consultButton')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'about':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('aboutTitle')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('aboutCompanyProfileTitle')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {t('aboutCompanyProfileDescription')}
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('aboutServiceAdvantagesTitle')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>{t('aboutServiceAdvantagesItem1')}</li>
                  <li>{t('aboutServiceAdvantagesItem2')}</li>
                  <li>{t('aboutServiceAdvantagesItem3')}</li>
                  <li>{t('aboutServiceAdvantagesItem4')}</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('aboutContactInfoTitle')}</h3>
                <div className="text-gray-600 text-sm space-y-1">
                  <p>{t('aboutContactInfoPhone')}</p>
                  <p>{t('aboutContactInfoEmail')}</p>
                  <p>{t('aboutContactInfoAddress')}</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">{t('logoInitial')}</span>
            </div>
            <h1 className="text-lg font-bold text-gray-800">{t('headerTitle')}</h1>
          </div>
          <div className="flex items-center space-x-4"> {/* Added a div to group login and switcher */}
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
              {t('loginButton')}
            </button>
            <LanguageSwitcher /> {/* Add the language switcher here */}
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        {renderContent()}
      </main>
    </div>
  );
}

export default function Home() {
  const t = useTranslations('Page');
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('loadingMessage')}</p>
        </div>
      </div>
    }>
      <HomeContent />
    </Suspense>
  );
}
